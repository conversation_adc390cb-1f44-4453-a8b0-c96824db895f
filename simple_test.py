"""
简化的性能测试脚本
"""

import time
import numpy as np
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.loader import DataLoader
from src.optimization.optimizer import StrategyOptimizer
from src.engine.engine import BacktestEngine
from src.strategy.pairs_strategy import PairsStrategy

def create_simple_test_data():
    """创建简单的测试数据"""
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    
    # 生成简单的价格序列
    np.random.seed(42)
    price1 = 100 + np.cumsum(np.random.randn(200) * 0.01)
    price2 = 50 + np.cumsum(np.random.randn(200) * 0.008)
    
    data = pd.DataFrame({
        '159915': price1,
        '518880': price2
    }, index=dates)
    
    return data

def test_single_backtest():
    """测试单次回测"""
    print("=== 测试单次回测 ===")
    
    data = create_simple_test_data()
    
    params = {
        'window': 30,
        'std_dev_mult': 2.0,
        'max_pos_size': 1.0
    }
    
    start_time = time.time()
    
    strategy = PairsStrategy(**params, verbose=False)
    engine = BacktestEngine(
        strategy=strategy,
        data=data,
        initial_capital=100000,
        commission_rate=0.0003,
        slippage_rate=0.0001,
        verbose=False
    )
    results = engine.run()
    
    end_time = time.time()
    
    print(f"单次回测时间: {end_time - start_time:.3f}秒")
    print(f"最终权益: {results['equity_curve'][-1]:,.2f}")
    print(f"交易次数: {len(results['trades'])}")
    
    return end_time - start_time

def test_optimization():
    """测试参数优化"""
    print("\n=== 测试参数优化 ===")
    
    data = create_simple_test_data()
    
    backtest_params = {
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'slippage_rate': 0.0001
    }
    
    # 小规模参数范围
    param_ranges = {
        'window': [20, 30],
        'std_dev_mult': [1.5, 2.0],
        'max_pos_size': [1.0]
    }
    
    print(f"参数组合数量: {2 * 2 * 1} = 4")
    
    # 测试单进程
    print("\n--- 单进程模式 ---")
    optimizer = StrategyOptimizer(data, backtest_params)
    
    start_time = time.time()
    try:
        best_params, best_results, all_results = optimizer.optimize(
            param_ranges=param_ranges,
            n_jobs=1,
            metric='total_returns'
        )
        single_time = time.time() - start_time
        print(f"单进程时间: {single_time:.3f}秒")
        print(f"最优参数: {best_params}")
        print(f"最优收益率: {((best_results['equity_curve'][-1] / best_results['equity_curve'][0]) - 1) * 100:.2f}%")
    except Exception as e:
        print(f"单进程优化失败: {e}")
        single_time = 0
    
    # 测试多进程
    print("\n--- 多进程模式 ---")
    start_time = time.time()
    try:
        best_params_mp, best_results_mp, all_results_mp = optimizer.optimize(
            param_ranges=param_ranges,
            n_jobs=2,
            metric='total_returns'
        )
        multi_time = time.time() - start_time
        print(f"多进程时间: {multi_time:.3f}秒")
        print(f"最优参数: {best_params_mp}")
        print(f"最优收益率: {((best_results_mp['equity_curve'][-1] / best_results_mp['equity_curve'][0]) - 1) * 100:.2f}%")
        
        if single_time > 0 and multi_time > 0:
            speedup = single_time / multi_time
            print(f"加速比: {speedup:.2f}x")
            
    except Exception as e:
        print(f"多进程优化失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始简化性能测试...")
    
    # 测试单次回测
    single_time = test_single_backtest()
    
    # 测试参数优化
    test_optimization()
    
    print(f"\n测试完成！单次回测时间: {single_time:.3f}秒")

if __name__ == "__main__":
    main()
