"""
实际场景的性能测试脚本
使用更大的数据集和参数范围来测试优化效果
"""

import time
import numpy as np
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.loader import DataLoader
from src.optimization.optimizer import StrategyOptimizer
from src.engine.engine import BacktestEngine
from src.strategy.pairs_strategy import PairsStrategy
# 回测参数配置
BACKTEST_PARAMS = {
    'start_date': '2022-11-27',
    'end_date': '2024-11-25',
    'initial_capital': 100000,
    'data_frequency': 'minute',  # 'day', 'minute', 'tick'
    'commission_rate': 0.00005,
    'slippage_rate': 0.001
}
def create_realistic_test_data():
    """创建更真实的测试数据"""
    # 初始化数据加载器
    data_loader = DataLoader()
    
    # 加载回测数据（只加载一次）
    print("正在加载数据...")
    data = data_loader.load_data(
        start_date=BACKTEST_PARAMS['start_date'],
        end_date=BACKTEST_PARAMS['end_date'],
        frequency=BACKTEST_PARAMS['data_frequency']
    )
    print(f"数据加载完成，范围: {data.index[0]} 到 {data.index[-1]}")
    
    return data

def test_realistic_optimization():
    """测试实际场景的参数优化"""
    print("=== 实际场景性能测试 ===")
    
    # 创建大数据集
    print("创建测试数据...")
    data = create_realistic_test_data()
    print(f"数据大小: {len(data)} 个时间点")
    
    backtest_params = {
        'initial_capital': 1000000,
        'commission_rate': 0.00005,
        'slippage_rate': 0.001
    }
    
    # 更大的参数范围
    param_ranges = {
        'window': [30, 60, 90, 120],
        'std_dev_mult': [1.0, 1.5, 2.0, 2.5, 3.0],
        'max_pos_size': [1.0]
    }
    
    total_combinations = np.prod([len(v) for v in param_ranges.values()])
    print(f"参数组合数量: {total_combinations}")
    
    optimizer = StrategyOptimizer(data, backtest_params)
    
    # 测试单进程
    print("\n--- 单进程模式 ---")
    start_time = time.time()
    try:
        best_params_single, best_results_single, all_results_single = optimizer.optimize(
            param_ranges=param_ranges,
            n_jobs=1,
            metric='total_returns'
        )
        single_time = time.time() - start_time
        
        print(f"单进程时间: {single_time:.1f}秒")
        print(f"最优参数: {best_params_single}")
        final_equity = best_results_single['equity_curve'][-1]
        total_return = (final_equity / backtest_params['initial_capital'] - 1) * 100
        print(f"最优收益率: {total_return:.2f}%")
        print(f"交易次数: {len(best_results_single['trades'])}")
        
    except Exception as e:
        print(f"单进程优化失败: {e}")
        single_time = 0
    
    # 测试多进程
    print(f"\n--- 多进程模式 (4进程) ---")
    start_time = time.time()
    try:
        best_params_multi, best_results_multi, all_results_multi = optimizer.optimize(
            param_ranges=param_ranges,
            n_jobs=4,
            metric='total_returns'
        )
        multi_time = time.time() - start_time
        
        print(f"多进程时间: {multi_time:.1f}秒")
        print(f"最优参数: {best_params_multi}")
        final_equity = best_results_multi['equity_curve'][-1]
        total_return = (final_equity / backtest_params['initial_capital'] - 1) * 100
        print(f"最优收益率: {total_return:.2f}%")
        print(f"交易次数: {len(best_results_multi['trades'])}")
        
        if single_time > 0 and multi_time > 0:
            speedup = single_time / multi_time
            print(f"\n加速比: {speedup:.2f}x")
            efficiency = speedup / 4 * 100
            print(f"并行效率: {efficiency:.1f}%")
            
    except Exception as e:
        print(f"多进程优化失败: {e}")
        import traceback
        traceback.print_exc()

def test_single_backtest_performance():
    """测试单次回测的性能"""
    print("=== 单次回测性能测试 ===")
    
    data = create_realistic_test_data()
    print(f"数据大小: {len(data)} 个时间点")
    
    params = {
        'window': 60,
        'std_dev_mult': 2.0,
        'max_pos_size': 1.0
    }
    
    # 运行多次测试
    times = []
    for i in range(3):
        start_time = time.time()
        
        strategy = PairsStrategy(**params, verbose=False)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=1000000,
            commission_rate=0.0003,
            slippage_rate=0.0001,
            verbose=False
        )
        results = engine.run()
        
        end_time = time.time()
        times.append(end_time - start_time)
        print(f"第 {i+1} 次: {times[-1]:.3f}秒")
    
    avg_time = np.mean(times)
    print(f"\n平均单次回测时间: {avg_time:.3f}秒")
    
    # 计算性能指标
    final_equity = results['equity_curve'][-1]
    total_return = (final_equity / 1000000 - 1) * 100
    print(f"收益率: {total_return:.2f}%")
    print(f"交易次数: {len(results['trades'])}")
    
    return avg_time

def main():
    """主函数"""
    print("开始实际场景性能测试...")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 单次回测性能
    single_backtest_time = test_single_backtest_performance()
    
    # 参数优化性能
    test_realistic_optimization()
    
    print(f"\n=== 性能总结 ===")
    print(f"单次回测平均时间: {single_backtest_time:.3f}秒")
    
    # 性能评估
    if single_backtest_time < 1.0:
        print("✓ 单次回测性能优秀")
    elif single_backtest_time < 3.0:
        print("✓ 单次回测性能良好")
    else:
        print("⚠ 单次回测性能需要优化")

if __name__ == "__main__":
    main()
