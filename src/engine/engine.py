import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Union
import os
import warnings
warnings.filterwarnings('ignore')

# 优化的数值计算函数
def fast_portfolio_update(cash: float, positions: np.ndarray, prices: np.ndarray) -> float:
    """快速计算投资组合权益"""
    return cash + np.sum(positions * prices)

def fast_drawdown_calculation(equity_curve: np.ndarray) -> float:
    """快速计算最大回撤"""
    peak = np.maximum.accumulate(equity_curve)
    drawdown = (peak - equity_curve) / peak
    return np.max(drawdown)

class BacktestEngine:
    """回测引擎核心类"""
    
    def __init__(
        self,
        strategy,
        data: pd.DataFrame,
        initial_capital: float = 1000000.0,
        commission_rate: float = 0.0003,
        slippage_rate: float = 0.0001,
        verbose: bool = True,
        output_dir: str = "results",
        **kwargs
    ):
        self.strategy = strategy
        self.data = data
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.verbose = verbose
        self.output_dir = output_dir
        
        # 创建输出目录（如果不存在）
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化账户状态
        self.portfolio = {
            'cash': initial_capital,
            'positions': {},
            'equity': initial_capital,
            'trades': []
        }

        # 预分配数组以提高性能
        n_bars = len(data)
        self.results = {
            'daily_returns': np.zeros(n_bars),
            'positions': [],
            'trades': [],
            'equity_curve': np.zeros(n_bars + 1),
            'benchmark_prices': []
        }
        self.results['equity_curve'][0] = initial_capital
        
        # 如果数据中包含518880，则保存其价格序列作为基准
        if '518880' in data.columns:
            self.results['benchmark_prices'] = data['518880'].tolist()
    
    def calculate_slippage(self, price: float, volume: int, direction: int) -> float:
        """计算滑点
        Args:
            price: 交易价格
            volume: 交易量
            direction: 交易方向(1: 买入, -1: 卖出)
        """
        return price + direction * self.slippage_rate
    
    def calculate_commission(self, price: float, volume: int) -> float:
        """计算交易费用"""
        return price * volume * self.commission_rate
    
    def execute_order(self, order: Dict) -> Dict:
        """执行订单
        Args:
            order: 订单信息字典
        Returns:
            交易记录
        """
        symbol = order['symbol']
        direction = order['direction']  # 1: 买入, -1: 卖出
        volume = order['volume']
        price = order['price']
        
        # 计算实际成交价格（考虑滑点）
        executed_price = self.calculate_slippage(price, volume, direction)
        
        # 计算交易费用
        commission = self.calculate_commission(executed_price, volume)
        
        # 计算交易金额
        trade_value = executed_price * volume
        
        # 更新现金
        self.portfolio['cash'] -= (trade_value * direction + commission)
        
        # 更新持仓
        if symbol not in self.portfolio['positions']:
            self.portfolio['positions'][symbol] = 0
        self.portfolio['positions'][symbol] += volume * direction
        
        # 记录交易
        trade = {
            'datetime': order['datetime'],
            'symbol': symbol,
            'direction': direction,
            'price': executed_price,
            'volume': volume,
            'commission': commission,
            'trade_value': trade_value
        }
        self.portfolio['trades'].append(trade)
        
        return trade
    
    def update_portfolio(self, current_prices: Dict[str, float]) -> None:
        """更新投资组合状态"""
        equity = self.portfolio['cash']
        
        for symbol, position in self.portfolio['positions'].items():
            if position != 0:
                equity += position * current_prices[symbol]
        
        self.portfolio['equity'] = equity
        
        # 记录权益曲线
        self.results['equity_curve'].append(equity)
        
        # 计算日收益率
        if len(self.results['equity_curve']) > 1:
            daily_return = (equity / self.results['equity_curve'][-2]) - 1
            self.results['daily_returns'].append(daily_return)
    
    def save_trades_to_csv(self, filename: Optional[str] = None) -> None:
        """
        将交易记录保存到CSV文件
        Args:
            filename: 输出文件名，不指定时使用默认名称
        """
        if not self.results['trades']:
            if self.verbose:
                print("没有交易记录可保存")
            return
        
        # 使用默认文件名或指定的文件名
        if filename is None:
            start_date = self.data.index[0].strftime('%Y%m%d')
            end_date = self.data.index[-1].strftime('%Y%m%d')
            pairs = self.strategy.pairs
            filename = f"trades_{pairs}_{start_date}_{end_date}.csv"
        
        # 构建完整文件路径
        filepath = os.path.join(self.output_dir, filename)
        
        # 将交易记录转换为DataFrame
        trades_df = pd.DataFrame(self.results['trades'])
        
        # 添加额外信息列
        trades_df['action'] = trades_df['direction'].apply(lambda x: '买入' if x > 0 else '卖出')
        trades_df['cash_after_trade'] = None
        trades_df['equity_after_trade'] = None
        
        # 添加交易后的现金和权益信息（通过模拟执行交易流程）
        cash = self.initial_capital
        positions = {}
        
        for i, trade in enumerate(self.results['trades']):
            # 更新现金
            cash -= (trade['trade_value'] * trade['direction'] + trade['commission'])
            
            # 更新持仓
            symbol = trade['symbol']
            if symbol not in positions:
                positions[symbol] = 0
            positions[symbol] += trade['volume'] * trade['direction']
            
            # 计算交易后的权益
            equity = cash
            # 获取交易发生时所有证券的价格
            trade_time = trade['datetime']
            prices_at_trade = {}
            
            # 找到最接近交易时间的价格数据
            closest_idx = self.data.index.get_indexer([trade_time], method='nearest')[0]
            if closest_idx >= 0 and closest_idx < len(self.data):
                price_row = self.data.iloc[closest_idx]
                for symbol in positions:
                    if symbol in price_row:
                        prices_at_trade[symbol] = price_row[symbol]
            
            # 计算持仓价值
            for symbol, pos in positions.items():
                if pos != 0 and symbol in prices_at_trade:
                    equity += pos * prices_at_trade[symbol]
            
            # 更新DataFrame
            trades_df.loc[i, 'cash_after_trade'] = cash
            trades_df.loc[i, 'equity_after_trade'] = equity
        
        # 保存到CSV
        trades_df.to_csv(filepath, index=False)
        
        if self.verbose:
            print(f"交易记录已保存至: {filepath}")
    
    def save_signals_to_csv(self, filename: Optional[str] = None) -> None:
        """
        将交易信号数据保存到CSV文件
        Args:
            filename: 输出文件名，不指定时使用默认名称
        """
        if not hasattr(self.strategy, 'signals') or self.strategy.signals is None:
            if self.verbose:
                print("没有信号数据可保存")
            return
        
        # 使用默认文件名或指定的文件名
        if filename is None:
            start_date = self.data.index[0].strftime('%Y%m%d')
            end_date = self.data.index[-1].strftime('%Y%m%d')
            pairs = self.strategy.pairs
            filename = f"signals_{pairs}_{start_date}_{end_date}.csv"
        
        # 构建完整文件路径
        filepath = os.path.join(self.output_dir, filename)
        
        # 复制信号数据并添加时间戳索引
        signals_df = self.strategy.signals.copy()
        
        # 保存到CSV
        signals_df.to_csv(filepath)
        
        if self.verbose:
            print(f"信号数据已保存至: {filepath}")
    
    def run(self) -> Dict:
        """运行回测"""
        if self.verbose:
            print(f"初始资金: {self.initial_capital:,.2f}")
        
        # 初始化策略
        self.strategy.initialize(self)
        
        # 保存价格数据
        self.results['prices'] = self.data.copy()

        # 保存信号数据
        if hasattr(self.strategy, 'signals') and self.strategy.signals is not None:
            # 创建信号字典
            signals_dict = {}

            # 检查是否为卡尔曼滤波器策略（有filtered_ratio字段）
            if 'filtered_ratio' in self.strategy.signals.columns:
                # 卡尔曼滤波器策略信号
                signals_dict = {
                    'price_ratio': self.strategy.signals['price_ratio'],
                    'filtered_ratio': self.strategy.signals['filtered_ratio'],
                    'upper_bound': self.strategy.signals['upper_bound'],
                    'lower_bound': self.strategy.signals['lower_bound'],
                    'uncertainty': self.strategy.signals['uncertainty']
                }
            else:
                # 传统滚动回归策略信号
                signals_dict = {
                    'ma': self.strategy.signals['ma'],
                    'raw_upper': self.strategy.signals['raw_upper'],
                    'raw_lower': self.strategy.signals['raw_lower'],
                    'price_ratio': self.strategy.signals['price_ratio']
                }

            self.results['signals'] = signals_dict

        # 转换数据为numpy数组以提高访问速度
        data_values = self.data.values
        data_columns = list(self.data.columns)
        data_index = self.data.index



        # 优化的主循环
        for i in range(len(data_values)):
            timestamp = data_index[i]

            # 直接从numpy数组获取价格数据（更快）
            current_prices = {}
            for j, col in enumerate(data_columns):
                current_prices[col] = data_values[i, j]

            # 快速计算权益
            equity = self.portfolio['cash']
            for symbol, position in self.portfolio['positions'].items():
                if position != 0:
                    equity += position * current_prices[symbol]

            self.portfolio['equity'] = equity
            self.results['equity_curve'][i + 1] = equity

            # 计算日收益率
            if i > 0:
                self.results['daily_returns'][i-1] = (equity / self.results['equity_curve'][i]) - 1

            # 运行策略（传递行数据）
            row_data = pd.Series(data_values[i], index=data_columns, name=timestamp)
            orders = self.strategy.on_bar(timestamp, row_data)

            # 批量执行订单
            if orders:
                for order in orders:
                    trade = self.execute_order(order)
                    self.results['trades'].append(trade)

            # 记录当前持仓（减少频率以提高性能）
            if i % 10 == 0 or i == len(data_values) - 1:  # 每10个时间点记录一次
                self.results['positions'].append({
                    'datetime': timestamp,
                    'positions': self.portfolio['positions'].copy()
                })
        
        # 计算回测结果
        self.calculate_results()
        
        # 保存交易记录和信号数据到CSV
        self.save_trades_to_csv()
        self.save_signals_to_csv()
        
        # 如果策略有save_orders_to_csv方法，则调用保存订单
        if hasattr(self.strategy, 'save_orders_to_csv') and callable(getattr(self.strategy, 'save_orders_to_csv')):
            self.strategy.save_orders_to_csv()
        
        # 打印交易统计
        if self.verbose:
            n_trades = len(self.results['trades'])
            if n_trades > 0:
                print(f"\n交易统计:")
                print(f"总交易次数: {n_trades}")
                total_commission = sum(trade['commission'] for trade in self.results['trades'])
                print(f"总手续费: {total_commission:,.2f}")
            
            print(f"最终权益: {self.portfolio['equity']:,.2f}")
            print(f"总收益率: {((self.portfolio['equity'] / self.initial_capital) - 1) * 100:.2f}%")
        
        return self.results
    
    def calculate_results(self) -> None:
        """计算回测结果统计指标（使用向量化操作）"""
        equity_curve = self.results['equity_curve']
        daily_returns = self.results['daily_returns']

        # 使用向量化操作计算所有指标
        total_return = (equity_curve[-1] - self.initial_capital) / self.initial_capital

        # 计算年化收益率
        days = len(daily_returns)
        annual_return = (1 + total_return) ** (252/days) - 1 if days > 0 else 0

        # 计算夏普比率（向量化）
        if len(daily_returns) > 0 and np.std(daily_returns) > 0:
            sharpe_ratio = np.sqrt(252) * np.mean(daily_returns) / np.std(daily_returns)
        else:
            sharpe_ratio = 0

        # 使用优化的最大回撤计算
        max_drawdown = fast_drawdown_calculation(equity_curve)

        self.results.update({
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'daily_returns': daily_returns.tolist()
        })