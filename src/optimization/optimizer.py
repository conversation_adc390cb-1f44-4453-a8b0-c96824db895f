import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from itertools import product
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.gridspec import GridSpec
from matplotlib.font_manager import FontProperties
import os
import pickle
import hashlib
from functools import lru_cache

from src.engine.engine import BacktestEngine
from src.strategy.pairs_strategy import PairsStrategy
from src.analysis.performance import PerformanceAnalyzer
from src.optimization.cache import get_cached_result, cache_result, get_data_hash

# 设置中文字体
font = FontProperties(family='Arial Unicode MS')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 全局函数用于并行处理（必须在模块级别定义）
def evaluate_single_params(args):
    """评估单个参数组合的全局函数"""
    params, data_dict, backtest_params = args

    try:
        # 重建DataFrame
        data = pd.DataFrame(data_dict['data'], index=pd.to_datetime(data_dict['index']))

        # 运行回测
        strategy = PairsStrategy(**params, verbose=False)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=backtest_params['initial_capital'],
            commission_rate=backtest_params['commission_rate'],
            slippage_rate=backtest_params['slippage_rate'],
            verbose=False
        )
        results = engine.run()

        # 只返回必要的指标，减少内存使用
        equity_curve = results['equity_curve']
        if len(equity_curve) > 0:
            total_return = (equity_curve[-1] / equity_curve[0]) - 1
        else:
            total_return = 0.0

        summary = {
            'total_return': total_return,
            'final_equity': equity_curve[-1] if len(equity_curve) > 0 else backtest_params['initial_capital'],
            'n_trades': len(results['trades'])
        }

        return total_return, summary

    except Exception as e:
        print(f"参数评估失败 {params}: {e}")
        import traceback
        traceback.print_exc()
        return 0.0, None

# 全局数据缓存
_data_cache = {}

def _cache_data(data: pd.DataFrame) -> str:
    """缓存数据并返回哈希值"""
    data_hash = hashlib.md5(str(data.values.tobytes()).encode()).hexdigest()
    _data_cache[data_hash] = data
    return data_hash

def _get_cached_data(data_hash: str) -> Optional[pd.DataFrame]:
    """从缓存获取数据"""
    return _data_cache.get(data_hash)

class StrategyOptimizer:
    """策略参数优化器"""

    def __init__(self, data: pd.DataFrame, backtest_params: Dict):
        self.data = data
        self.backtest_params = backtest_params
        self.initial_capital = backtest_params.get('initial_capital', 1000000)
        self.commission_rate = backtest_params.get('commission_rate', 0.0001)
        self.slippage_rate = backtest_params.get('slippage_rate', 0.0001)

        # 缓存数据以供并行处理使用
        self.data_hash = _cache_data(data)

        # 数据哈希用于缓存
        self.data_hash_for_cache = get_data_hash(data)
        
    def _split_data(self, data: pd.DataFrame, n_splits: int = 3) -> List[Tuple[pd.DataFrame, pd.DataFrame]]:
        """时间序列交叉验证数据分割"""
        splits = []
        segment_size = len(data) // n_splits
        
        for i in range(n_splits - 1):
            split_idx = (i + 1) * segment_size
            splits.append((
                data.iloc[:split_idx],
                data.iloc[split_idx:split_idx + segment_size]
            ))
        
        return splits

    def _calculate_metrics(self, equity_data: pd.DataFrame, trades: List[Dict]) -> pd.DataFrame:
        """计算回测指标"""
        # 基础计算
        total_return = (equity_data['equity'].iloc[-1] / equity_data['equity'].iloc[0]) - 1
        annual_return = (1 + total_return) ** (252/len(equity_data)) - 1
        
        daily_returns = equity_data['daily_returns']
        sharpe = np.sqrt(252) * daily_returns.mean() / daily_returns.std() if len(daily_returns) > 0 and daily_returns.std() > 0 else 0
        
        # 计算胜率
        win_rate = 0
        if trades:
            profitable_trades = len([t for t in trades if t['direction'] * (t['price'] - t['commission']) > 0])
            win_rate = profitable_trades / len(trades)
        
        return pd.DataFrame({
            'Value': [
                f"{annual_return*100:.2f}%",
                f"{win_rate*100:.2f}%",
                f"{sharpe:.2f}"
            ]
        }, index=['ETF1年化持仓增长率', 'ETF1换仓胜率', 'ETF1持仓夏普比率'])

    def _evaluate_params(self, params: Dict, data: pd.DataFrame) -> Tuple[float, Dict]:
        """评估单个参数组合"""
        # 运行回测
        strategy = PairsStrategy(**params, verbose=False)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=self.initial_capital,
            commission_rate=self.commission_rate,
            slippage_rate=self.slippage_rate,
            verbose=False
        )
        results = engine.run()
        
        # 计算总收益率
        equity_curve = pd.DataFrame({
            'equity': results['equity_curve'][1:],
            'daily_returns': results['daily_returns']
        }, index=data.index)
        
        total_return = (equity_curve['equity'].iloc[-1] / equity_curve['equity'].iloc[0]) - 1
        
        return total_return, results

    def _evaluate_params_fast(self, params: Dict) -> Tuple[float, Dict]:
        """快速评估参数组合（只返回关键指标）"""
        # 首先检查缓存
        cached_result = get_cached_result(params, self.data_hash_for_cache, self.backtest_params)
        if cached_result is not None:
            return cached_result['score'], cached_result['summary']

        try:
            # 运行回测
            strategy = PairsStrategy(**params, verbose=False)
            engine = BacktestEngine(
                strategy=strategy,
                data=self.data,
                initial_capital=self.initial_capital,
                commission_rate=self.commission_rate,
                slippage_rate=self.slippage_rate,
                verbose=False
            )
            results = engine.run()

            # 只计算必要的指标
            equity_curve = results['equity_curve']
            total_return = (equity_curve[-1] / equity_curve[0]) - 1

            summary = {
                'total_return': total_return,
                'final_equity': equity_curve[-1],
                'n_trades': len(results['trades'])
            }

            # 缓存结果
            cache_result(params, self.data_hash_for_cache, self.backtest_params, {
                'score': total_return,
                'summary': summary
            })

            return total_return, summary

        except Exception as e:
            if self.backtest_params.get('verbose', False):
                print(f"参数评估失败 {params}: {e}")
            return 0.0, None

    def optimize(self, param_ranges: Dict[str, List], n_jobs: int = 4, metric: str = 'total_returns') -> Tuple[Dict, Dict, List]:
        """优化策略参数

        Args:
            param_ranges: 参数范围字典
            n_jobs: 并行进程数，8表示使用所有可用CPU，1表示使用单进程
            metric: 优化指标，目前支持 'total_returns'

        Returns:
            tuple: (最优参数, 最优参数的回测结果, 所有参数组合的结果)
        """
        # 生成参数组合
        param_names = list(param_ranges.keys())
        param_values = list(product(*param_ranges.values()))

        print(f"开始优化 {len(param_values)} 组参数，使用 {n_jobs} 个进程...")

        results = []

        if n_jobs == 1:
            # 单进程模式
            print("使用单进程模式...")
            for i, values in enumerate(param_values):
                params = dict(zip(param_names, values))
                if i % 10 == 0:
                    print(f"进度: {i+1}/{len(param_values)} ({(i+1)/len(param_values)*100:.1f}%)")

                score, summary = self._evaluate_params_fast(params)
                results.append({
                    'params': params,
                    'score': score,
                    'summary': summary
                })
        else:
            # 并行评估模式
            print(f"使用 {n_jobs} 个进程并行评估...")

            # 准备参数 - 直接传递数据字典
            data_dict = {
                'data': self.data.to_dict(),
                'index': self.data.index.strftime('%Y-%m-%d %H:%M:%S').tolist()
            }
            args_list = [
                (dict(zip(param_names, values)), data_dict, self.backtest_params)
                for values in param_values
            ]

            with ProcessPoolExecutor(max_workers=n_jobs) as executor:
                # 提交所有任务
                future_to_params = {
                    executor.submit(evaluate_single_params, args): dict(zip(param_names, values))
                    for args, values in zip(args_list, param_values)
                }

                # 收集结果
                completed = 0
                for future in as_completed(future_to_params):
                    params = future_to_params[future]
                    try:
                        score, summary = future.result()
                        results.append({
                            'params': params,
                            'score': score,
                            'summary': summary
                        })
                    except Exception as e:
                        print(f"参数 {params} 评估失败: {e}")
                        results.append({
                            'params': params,
                            'score': 0.0,
                            'summary': None
                        })

                    completed += 1
                    if completed % 10 == 0:
                        print(f"进度: {completed}/{len(param_values)} ({completed/len(param_values)*100:.1f}%)")

        # 获取最优结果
        valid_results = [r for r in results if r['summary'] is not None]
        if not valid_results:
            raise ValueError("没有有效的参数组合结果")

        valid_results.sort(key=lambda x: x['score'], reverse=True)
        best_result = valid_results[0]

        # 使用最优参数重新运行完整回测以获取详细结果
        print(f"\n最优参数: {best_result['params']}")
        print("重新运行最优参数的完整回测...")
        _, best_backtest_results = self._evaluate_params(best_result['params'], self.data)

        return best_result['params'], best_backtest_results, results

    def plot_optimization_results(self, all_results: List[Dict]):
        """可视化优化结果
        
        Args:
            all_results: 所有参数组合的结果列表
        """
        # 创建结果目录
        os.makedirs('results', exist_ok=True)
        
        # 转换结果为DataFrame
        results_data = []
        for result in all_results:
            result_dict = {
                'total_return': result['score'] * 100,  # 转换为百分比
                **result['params']
            }
            results_data.append(result_dict)
        
        results_df = pd.DataFrame(results_data)
        
        # 创建图表
        plt.figure(figsize=(15, 10))
        
        # 1. 参数分布箱线图
        plt.subplot(2, 1, 1)
        param_names = [col for col in results_df.columns if col != 'total_return']
        box_data = []
        labels = []
        
        for param in param_names:
            for value in sorted(results_df[param].unique()):
                returns = results_df[results_df[param] == value]['total_return']
                box_data.append(returns)
                labels.append(f'{param}={value}')
        
        plt.boxplot(box_data, labels=labels)
        plt.xticks(rotation=45)
        plt.title('不同参数值的收益率分布')
        plt.ylabel('总收益率 (%)')
        
        # 2. 参数热力图（如果有两个以上的参数）
        if len(param_names) >= 2:
            plt.subplot(2, 1, 2)
            pivot_table = results_df.pivot_table(
                values='total_return',
                index=param_names[0],
                columns=param_names[1],
                aggfunc='mean'
            )
            sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlGn')
            plt.title('参数组合的平均收益率热力图')
        
        plt.tight_layout()
        plt.savefig('results/optimization_results.png')
        plt.close()
        
        # 保存参数优化结果到CSV
        results_df.sort_values('total_return', ascending=False).to_csv('results/optimization_results.csv', index=False)
        
        print(f"\n参数优化结果已保存到: results/optimization_results.png 和 results/optimization_results.csv")