"""
优化缓存机制
用于缓存回测结果，避免重复计算相同的参数组合
"""

import os
import pickle
import hashlib
import pandas as pd
from typing import Dict, Any, Optional
import json

class OptimizationCache:
    """优化结果缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def _get_cache_key(self, params: Dict, data_hash: str, backtest_params: Dict) -> str:
        """生成缓存键"""
        # 创建包含所有相关参数的字典
        cache_dict = {
            'params': params,
            'data_hash': data_hash,
            'backtest_params': {
                'initial_capital': backtest_params.get('initial_capital'),
                'commission_rate': backtest_params.get('commission_rate'),
                'slippage_rate': backtest_params.get('slippage_rate')
            }
        }
        
        # 生成哈希
        cache_str = json.dumps(cache_dict, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get(self, params: Dict, data_hash: str, backtest_params: Dict) -> Optional[Dict]:
        """从缓存获取结果"""
        cache_key = self._get_cache_key(params, data_hash, backtest_params)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception:
                # 缓存文件损坏，删除它
                os.remove(cache_file)
        
        return None
    
    def set(self, params: Dict, data_hash: str, backtest_params: Dict, result: Dict) -> None:
        """保存结果到缓存"""
        cache_key = self._get_cache_key(params, data_hash, backtest_params)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
        except Exception as e:
            print(f"缓存保存失败: {e}")
    
    def clear(self) -> None:
        """清空缓存"""
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.pkl'):
                os.remove(os.path.join(self.cache_dir, filename))
    
    def get_cache_size(self) -> int:
        """获取缓存文件数量"""
        return len([f for f in os.listdir(self.cache_dir) if f.endswith('.pkl')])

def get_data_hash(data: pd.DataFrame) -> str:
    """计算数据的哈希值"""
    return hashlib.md5(str(data.values.tobytes()).encode()).hexdigest()

# 全局缓存实例
_global_cache = OptimizationCache()

def get_cached_result(params: Dict, data_hash: str, backtest_params: Dict) -> Optional[Dict]:
    """获取缓存结果的便捷函数"""
    return _global_cache.get(params, data_hash, backtest_params)

def cache_result(params: Dict, data_hash: str, backtest_params: Dict, result: Dict) -> None:
    """缓存结果的便捷函数"""
    _global_cache.set(params, data_hash, backtest_params, result)

def clear_cache() -> None:
    """清空缓存的便捷函数"""
    _global_cache.clear()

def get_cache_info() -> Dict[str, Any]:
    """获取缓存信息"""
    return {
        'cache_size': _global_cache.get_cache_size(),
        'cache_dir': _global_cache.cache_dir
    }
