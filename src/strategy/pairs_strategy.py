import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union
from statsmodels.regression.linear_model import OLS
from statsmodels.regression.rolling import RollingOLS
import datetime
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def fast_rolling_regression(y: np.ndarray, x: np.ndarray, window: int) -> tuple:
    """
    快速滚动回归计算
    返回: (beta_values, beta_std_values)
    """
    n = len(y)
    beta_values = np.full(n, np.nan)
    beta_std_values = np.full(n, np.nan)

    for i in range(window, n):
        y_window = y[i-window:i]
        x_window = x[i-window:i]

        # 计算回归系数
        variance = np.dot(x_window, x_window)
        beta = np.dot(x_window, y_window) / np.dot(x_window, x_window)

        # 计算残差和标准误差
        y_pred = beta * x_window
        residuals = y_window - y_pred
        mse = np.mean(residuals ** 2)
        beta_std = np.sqrt(mse / (variance * (window - 1)))

        beta_values[i] = beta
        beta_std_values[i] = beta_std

    return beta_values, beta_std_values

class PairsStrategy:
    """ETF配对交易策略 - 使用基于滚动回归和beta标准误差计算交易区间"""
    
    def __init__(
        self,
        window: int = 10,
        std_dev_mult: float = 1.2,
        max_pos_size: float = 1.0,
        verbose: bool = True,
        output_dir: str = "results",
    ):
        """
        初始化策略参数
        Args:
            window: 回归窗口大小（使用过去多少个交易日的数据点）
            std_dev_mult: beta标准误差的乘数，用于计算交易区间
            max_pos_size: 最大持仓比例
            verbose: 是否输出交易日志
            output_dir: 输出目录，用于保存订单记录
        """
        self.window = window
        self.std_dev_mult = std_dev_mult
        self.max_pos_size = max_pos_size
        self.verbose = verbose
        self.output_dir = output_dir
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 运行时数据
        self.engine: Optional[object] = None
        self.pairs: List[str] = []
        self.current_position: Optional[str] = None  # 记录当前持仓的ETF

        # 存储历史数据和信号
        self.price_history: Optional[pd.DataFrame] = None
        self.signals: Optional[pd.DataFrame] = None
        self.commission_rate: float = 0.0003  # 默认值，将在initialize时从engine获取
        self.slippage_rate: float = 0.0001

        # 存储生成的订单
        self.all_orders = []

        # 价格比率盈亏检查机制相关变量
        self.last_trade_info = None  # 存储上次交易信息: {'timestamp', 'price_ratio', 'direction', 'target_etf'}
        self.profit_check_enabled = False  # 是否启用盈亏检查
        self.profit_check_log = []  # 记录盈亏检查的日志
    
    def initialize(self, engine) -> None:
        """策略初始化"""
        self.engine = engine
        self.pairs = list(engine.data.columns)
        if len(self.pairs) != 2:
            raise ValueError("策略需要两个ETF的数据")
        
        # 获取手续费率
        self.commission_rate = engine.commission_rate
        self.slippage_rate = engine.slippage_rate
        
        # 初始化历史数据DataFrame
        self.price_history = engine.data.copy()
        self.calculate_signals()
    
    def calculate_signals(self) -> None:
        """
        计算所有交易信号
        - 使用优化的滚动回归实现
        - 基于回归的beta和beta标准误差计算交易区间
        - 考虑交易手续费和滑点的影响
        - 向量化计算以提高效率
        """
        if self.price_history is None:
            return

        etf1, etf2 = self.pairs

        # 计算价格比率
        price_ratio = self.price_history[etf1] / self.price_history[etf2]

        # 准备回归数据
        y = np.array(self.price_history[etf1].values, dtype=np.float64)
        x = np.array(self.price_history[etf2].values, dtype=np.float64)

        # 使用优化的滚动回归
        try:
            # 使用快速滚动回归函数
            beta_values, beta_std_values = fast_rolling_regression(y, x, self.window)

            # 转换为pandas Series并向前移动一位（避免未来信息泄露）
            beta_series = pd.Series(beta_values, index=self.price_history.index).shift(1)
            beta_std_series = pd.Series(beta_std_values, index=self.price_history.index).shift(1)

            # 计算交易区间
            upper_values = beta_series + self.std_dev_mult * beta_std_series
            lower_values = beta_series - self.std_dev_mult * beta_std_series

            # 计算考虑手续费和滑点的交易边界
            cost_factor = ((1 + self.commission_rate) * (1 + self.slippage_rate))**2
            trading_lower = lower_values / cost_factor
            trading_upper = upper_values * cost_factor
            
            # 创建信号DataFrame
            self.signals = pd.DataFrame({
                'ma': beta_values,  # 使用滚动beta作为均值
                'std': beta_std_values,  # 使用滚动beta标准误差
                'upper_bound': trading_upper,  # 实际交易用的上边界（含手续费）
                'lower_bound': trading_lower,  # 实际交易用的下边界（含手续费）
                'raw_upper': upper_values,     # 原始上边界（不含手续费）
                'raw_lower': lower_values,     # 原始下边界（不含手续费）
                'etf2_price': self.price_history[etf2],
                'etf1_price': self.price_history[etf1],
                'price_ratio': price_ratio,
                'beta': beta_values  # 保存beta值
            }, index=self.price_history.index)
            
            # 预计算交易信号
            # 当价格比率 > 上限时：卖出ETF1, 买入ETF2 (卖高买低) -> 信号为-1
            # 当价格比率 < 下限时：买入ETF1, 卖出ETF2 (买低卖高) -> 信号为1
            self.signals['trade_signal'] = np.select(
                [
                    self.signals['price_ratio'] > self.signals['upper_bound'],
                    self.signals['price_ratio'] < self.signals['lower_bound']
                ],
                [-1, 1],  # -1表示卖ETF1买ETF2，1表示买ETF1卖ETF2
                default=0  # 默认不交易
            )
            
            # 创建索引位置映射，用于快速查找
            self.timestamp_to_idx = {ts: i for i, ts in enumerate(self.signals.index)}
            
        except Exception as e:
            if self.verbose:
                print(f"Rolling regression failed: {e}")
                import traceback
                traceback.print_exc()
    
    def calculate_position_size(self, price: float) -> int:
        """
        计算持仓规模
        Args:
            price: 交易价格
        Returns:
            交易数量
        """
        available_capital = self.engine.portfolio['cash'] * self.max_pos_size
        
        # 确保返回非负值
        size = max(0, int(available_capital / price))
        
        if self.verbose and available_capital <= 0:
            print(f"警告: 可用资金为{available_capital:.2f}，无法计算有效的持仓规模")
        
        return size

    def check_profit_potential(self, current_timestamp: pd.Timestamp, current_price_ratio: float,
                              new_trade_signal: int, target_etf: str) -> Dict:
        """
        检查如果执行反向交易（平仓）是否会盈利

        Args:
            current_timestamp: 当前时间戳
            current_price_ratio: 当前价格比率
            new_trade_signal: 新的交易信号 (1 或 -1)
            target_etf: 目标ETF

        Returns:
            包含盈亏检查结果的字典
        """
        check_result = {
            'can_trade': True,
            'reason': '无历史交易记录，允许交易',
            'expected_profit': 0.0,
            'last_trade_ratio': None,
            'current_ratio': current_price_ratio
        }

        # 如果没有历史交易记录或者盈亏检查被禁用，允许交易
        if not self.last_trade_info or not self.profit_check_enabled:
            return check_result

        last_trade = self.last_trade_info
        last_price_ratio = last_trade['price_ratio']
        last_direction = last_trade['direction']  # 1: 买ETF1卖ETF2, -1: 卖ETF1买ETF2
        last_target_etf = last_trade['target_etf']

        check_result['last_trade_ratio'] = last_price_ratio

        # 如果目标ETF相同，说明不是反向交易，允许交易
        if target_etf == last_target_etf:
            check_result['reason'] = '相同方向交易，允许执行'
            return check_result

        # 计算如果现在平仓的预期盈亏
        # 上次交易方向：1 (买ETF1卖ETF2) -> 现在要卖ETF1买ETF2 -> 盈利条件：当前比率 > 上次比率
        # 上次交易方向：-1 (卖ETF1买ETF2) -> 现在要买ETF1卖ETF2 -> 盈利条件：当前比率 < 上次比率

        if last_direction == 1:  # 上次买ETF1卖ETF2
            # 现在要卖ETF1买ETF2，盈利条件：当前比率 > 上次比率
            expected_profit = (current_price_ratio - last_price_ratio) / last_price_ratio
            will_profit = current_price_ratio > last_price_ratio
        else:  # 上次卖ETF1买ETF2
            # 现在要买ETF1卖ETF2，盈利条件：当前比率 < 上次比率
            expected_profit = (last_price_ratio - current_price_ratio) / last_price_ratio
            will_profit = current_price_ratio < last_price_ratio

        check_result['expected_profit'] = expected_profit

        # 考虑交易成本（手续费和滑点）
        total_cost = 2 * (self.commission_rate + self.slippage_rate)  # 双边成本

        if will_profit and expected_profit > total_cost:
            check_result['can_trade'] = True
            check_result['reason'] = f'预期盈利 {expected_profit:.4f} > 交易成本 {total_cost:.4f}，允许交易'
        else:
            check_result['can_trade'] = False
            if not will_profit:
                check_result['reason'] = f'反向交易会亏损，预期收益率 {expected_profit:.4f}，跳过交易'
            else:
                check_result['reason'] = f'预期盈利 {expected_profit:.4f} < 交易成本 {total_cost:.4f}，跳过交易'

        return check_result

    def record_trade_info(self, timestamp: pd.Timestamp, price_ratio: float,
                         trade_signal: int, target_etf: str) -> None:
        """
        记录交易信息用于下次盈亏检查

        Args:
            timestamp: 交易时间戳
            price_ratio: 交易时的价格比率
            trade_signal: 交易信号 (1 或 -1)
            target_etf: 目标ETF
        """
        self.last_trade_info = {
            'timestamp': timestamp,
            'price_ratio': price_ratio,
            'direction': trade_signal,
            'target_etf': target_etf
        }

    def on_bar(self, timestamp: pd.Timestamp, row) -> List[Dict]:
        """
        处理每个时间点的数据
        Args:
            timestamp: 当前时间戳
            row: 当前数据行（namedtuple）
        Returns:
            订单列表
        """
        orders = []
        etf1, etf2 = self.pairs
        
        # 使用索引直接获取当前时间点的信号（避免使用.loc，速度更快）
        idx = self.timestamp_to_idx.get(timestamp)
        if idx is None:
            return orders  # 找不到对应的时间戳，不交易
        
        # 使用 .iat 直接通过整数索引访问，比.loc更快
        try:
            if self.signals is None:
                return orders

            ma = self.signals['ma'].iat[idx]
            std = self.signals['std'].iat[idx]

            # 如果数据不足，不进行交易
            if pd.isna(ma) or pd.isna(std):
                return orders

            # 获取预计算的交易信号
            trade_signal = self.signals['trade_signal'].iat[idx]
        except (IndexError, KeyError):
            return orders
        
        # 根据交易信号决定交易目标
        if trade_signal == 0:
            return orders  # 无交易信号
        
        # 确定目标ETF
        target_etf = etf1 if trade_signal == 1 else etf2
            
        # 如果需要切换持仓
        if self.current_position != target_etf:
            # 直接从索引获取价格比率和边界，比使用.loc更快
            price_ratio = self.signals['price_ratio'].iat[idx]
            upper_bound = self.signals['upper_bound'].iat[idx]
            lower_bound = self.signals['lower_bound'].iat[idx]

            # 执行盈亏检查
            profit_check = self.check_profit_potential(timestamp, price_ratio, trade_signal, target_etf)

            # 记录盈亏检查日志
            log_entry = {
                'timestamp': timestamp,
                'price_ratio': price_ratio,
                'trade_signal': trade_signal,
                'target_etf': target_etf,
                'can_trade': profit_check['can_trade'],
                'reason': profit_check['reason'],
                'expected_profit': profit_check['expected_profit'],
                'last_trade_ratio': profit_check['last_trade_ratio']
            }
            self.profit_check_log.append(log_entry)

            if self.verbose:
                print(f"{timestamp}: 切换持仓信号")
                print(f"价格比率: {price_ratio:.4f}, 上界: {upper_bound:.4f}, 下界: {lower_bound:.4f}")
                print(f"从 {self.current_position if self.current_position else '无持仓'} 切换到 {target_etf}")
                print(f"盈亏检查: {profit_check['reason']}")

            # 如果盈亏检查不通过，跳过本次交易
            if not profit_check['can_trade']:
                if self.verbose:
                    print(f"跳过交易: {profit_check['reason']}")
                return orders
            
            # 计算平仓后的预期可用资金
            expected_cash = self.engine.portfolio['cash']
            for symbol in [etf1, etf2]:
                position = self.engine.portfolio['positions'].get(symbol, 0)
                if position != 0:
                    # 估算平仓后会增加的资金
                    symbol_price = getattr(row, f"_{self.pairs.index(symbol)+1}") if hasattr(row, "_1") else row[symbol]
                    expected_cash += abs(position) * symbol_price
                    # 减去手续费
                    expected_cash -= abs(position) * symbol_price * self.commission_rate
            
            # 先清空所有持仓
            for symbol in [etf1, etf2]:
                position = self.engine.portfolio['positions'].get(symbol, 0)
                if position != 0:
                    # 针对namedtuple类型的row获取价格
                    price = getattr(row, f"_{self.pairs.index(symbol)+1}") if hasattr(row, "_1") else row[symbol]
                    order = {
                        'symbol': symbol,
                        'direction': -1 if position > 0 else 1,  # 平仓方向与持仓相反
                        'volume': abs(position),
                        'price': price,
                        'datetime': timestamp
                    }
                    orders.append(order)
                    
                    # 添加到全部订单列表
                    self.all_orders.append(order)
            
            # 针对namedtuple类型的row获取价格
            target_price = getattr(row, f"_{self.pairs.index(target_etf)+1}") if hasattr(row, "_1") else row[target_etf]
            
            # 使用预期可用资金计算新持仓数量
            # 考虑留出一部分资金作为手续费和滑点
            available_capital = expected_cash * self.max_pos_size
            size = int(available_capital / target_price)
            
            if self.verbose:
                print(f"目标ETF: {target_etf}, 价格: {target_price:.4f}")
                print(f"当前现金: {self.engine.portfolio['cash']:.2f}, 预期可用资金: {expected_cash:.2f}")
                print(f"计算出的持仓规模: {size}")
            
            # 建立新持仓（确保size为正数）
            if size > 0:
                order = {
                    'symbol': target_etf,
                    'direction': 1,  # 买入
                    'volume': size,
                    'price': target_price,
                    'datetime': timestamp
                }
                orders.append(order)
                
                # 添加到全部订单列表
                self.all_orders.append(order)
            else:
                if self.verbose:
                    print(f"警告: 计算出的持仓规模为{size}，小于等于0，跳过买入{target_etf}")
            
            self.current_position = target_etf

            # 记录本次交易信息用于下次盈亏检查
            self.record_trade_info(timestamp, price_ratio, trade_signal, target_etf)

        return orders
    
    def save_orders_to_csv(self) -> None:
        """
        将策略生成的所有订单保存到CSV文件
        """
        if not self.all_orders:
            if self.verbose:
                print("没有订单记录可保存")
            return
        
        # 构建文件名
        start_date = self.price_history.index[0].strftime('%Y%m%d')
        end_date = self.price_history.index[-1].strftime('%Y%m%d')
        pairs_str = self.pairs
        
        filename = f"orders_{pairs_str}_{start_date}_{end_date}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # 转换为DataFrame并保存
        orders_df = pd.DataFrame(self.all_orders)
        
        # 添加订单类型字段
        orders_df['type'] = orders_df.apply(
            lambda x: '买入' if x['direction'] > 0 else '卖出', 
            axis=1
        )
        
        # 添加交易原因字段（根据当时的信号）
        orders_df['reason'] = None
        for i, order in orders_df.iterrows():
            timestamp = order['datetime']
            if timestamp in self.signals.index:
                signal_data = self.signals.loc[timestamp]
                price_ratio = signal_data['price_ratio']
                upper_bound = signal_data['upper_bound'] 
                lower_bound = signal_data['lower_bound']
                
                if price_ratio > upper_bound:
                    reason = f"价格比率({price_ratio:.6f})高于上界({upper_bound:.6f})"
                elif price_ratio < lower_bound:
                    reason = f"价格比率({price_ratio:.6f})低于下界({lower_bound:.6f})"
                else:
                    reason = "信号变化或持仓调整"
                    
                orders_df.at[i, 'reason'] = reason
        
        # 保存到CSV
        orders_df.to_csv(filepath, index=False)
        
        if self.verbose:
            print(f"订单记录已保存至: {filepath}")

        # 同时保存盈亏检查日志
        self.save_profit_check_log()

    def save_profit_check_log(self) -> None:
        """
        保存盈亏检查日志到CSV文件
        """
        if not self.profit_check_log:
            if self.verbose:
                print("没有盈亏检查日志可保存")
            return

        # 构建文件名
        start_date = self.price_history.index[0].strftime('%Y%m%d') if self.price_history is not None else 'unknown'
        end_date = self.price_history.index[-1].strftime('%Y%m%d') if self.price_history is not None else 'unknown'
        pairs_str = self.pairs

        filename = f"profit_check_log_{pairs_str}_{start_date}_{end_date}.csv"
        filepath = os.path.join(self.output_dir, filename)

        # 转换为DataFrame并保存
        log_df = pd.DataFrame(self.profit_check_log)

        # 添加结果描述
        log_df['result'] = log_df.apply(
            lambda x: '允许交易' if x['can_trade'] else '跳过交易',
            axis=1
        )

        # 保存到CSV
        log_df.to_csv(filepath, index=False)

        if self.verbose:
            print(f"盈亏检查日志已保存至: {filepath}")

            # 统计信息
            total_checks = len(log_df)
            allowed_trades = len(log_df[log_df['can_trade'] == True])
            skipped_trades = total_checks - allowed_trades

            print(f"盈亏检查统计: 总检查次数={total_checks}, 允许交易={allowed_trades}, 跳过交易={skipped_trades}")
            if total_checks > 0:
                print(f"交易通过率: {allowed_trades/total_checks:.2%}")

    def analyze_signal_at_time(self, timestamp: Union[str, pd.Timestamp], verbose: bool = True) -> Dict:
        """
        分析特定时间点的信号数据
        
        Args:
            timestamp: 时间戳或字符串格式的时间 (例如 '2024-11-22 10:28:00')
            verbose: 是否打印详细信息
            
        Returns:
            包含该时间点信号数据的字典
        """
        # 如果输入是字符串，转换为Timestamp
        if isinstance(timestamp, str):
            timestamp = pd.Timestamp(timestamp)
        
        # 查找最接近的时间点
        if timestamp not in self.signals.index:
            closest_idx = self.signals.index.get_indexer([timestamp], method='nearest')[0]
            timestamp = self.signals.index[closest_idx]
            if verbose:
                print(f"找到最接近的时间点: {timestamp}")
        
        # 获取该时间点的信号数据
        signal_data = self.signals.loc[timestamp].to_dict()
        
        # 计算额外信息：与上下边界的距离百分比
        price_ratio = signal_data['price_ratio']
        upper_bound = signal_data['upper_bound']
        lower_bound = signal_data['lower_bound']
        
        signal_data['distance_to_upper_pct'] = (upper_bound - price_ratio) / price_ratio * 100 if not pd.isna(upper_bound) else None
        signal_data['distance_to_lower_pct'] = (price_ratio - lower_bound) / price_ratio * 100 if not pd.isna(lower_bound) else None
        
        # 判断是否触发交易信号
        trade_signal = signal_data['trade_signal']
        signal_data['signal_text'] = '无信号' if trade_signal == 0 else ('买入' + self.pairs[0] + ',卖出' + self.pairs[1] if trade_signal == 1 else '卖出' + self.pairs[0] + ',买入' + self.pairs[1])
        
        if verbose:
            etf1, etf2 = self.pairs
            print(f"\n===== {timestamp} 信号分析 =====")
            print(f"ETF对: {etf1} - {etf2}")
            print(f"ETF1 价格: {signal_data['etf1_price']:.4f}")
            print(f"ETF2 价格: {signal_data['etf2_price']:.4f}")
            print(f"价格比率: {price_ratio:.6f}")
            print(f"移动平均(Beta): {signal_data['ma']:.6f}")
            print(f"交易上界: {upper_bound:.6f} (距离当前: {signal_data['distance_to_upper_pct']:.2f}%)")
            print(f"交易下界: {lower_bound:.6f} (距离当前: {signal_data['distance_to_lower_pct']:.2f}%)")
            print(f"标准差: {signal_data['std']:.6f}")
            print(f"交易信号: {signal_data['signal_text']}")
            
            # 计算窗口起始时间（用于说明该信号基于哪个时间段的数据）
            window_size = self.window
            all_timestamps = self.signals.index
            current_idx = all_timestamps.get_loc(timestamp)
            
            if current_idx >= window_size:
                window_start = all_timestamps[current_idx - window_size + 1]
                print(f"\n此信号基于: {window_start} 至 {timestamp} 的数据计算")
        
        return signal_data 