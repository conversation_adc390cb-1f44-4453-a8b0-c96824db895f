import numpy as np
import pandas as pd
import time
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

from .pairs_strategy import PairsStrategy
from .kalman_pairs_strategy import KalmanPairsStrategy

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceBenchmark:
    """
    性能基准测试工具
    
    比较卡尔曼滤波器策略与传统滚动回归策略的性能差异：
    1. 计算时间对比
    2. 内存使用对比
    3. 信号质量对比
    4. 实时处理能力测试
    """
    
    def __init__(self, data: pd.DataFrame, verbose: bool = True):
        """
        初始化基准测试
        
        Args:
            data: 测试数据
            verbose: 是否输出详细信息
        """
        self.data = data
        self.verbose = verbose
        self.results = {}
        
    def benchmark_computation_time(
        self, 
        rolling_windows: List[int] = [10, 20, 40, 60],
        kalman_params: Dict = None
    ) -> Dict:
        """
        基准测试计算时间
        
        Args:
            rolling_windows: 滚动回归窗口大小列表
            kalman_params: 卡尔曼滤波器参数
            
        Returns:
            计算时间对比结果
        """
        if kalman_params is None:
            kalman_params = {
                'process_noise_ratio': 1e-4,
                'measurement_noise_ratio': 1e-2,
                'trading_threshold': 2.0
            }
        
        results = {
            'rolling_regression': {},
            'kalman_filter': {},
            'data_size': len(self.data)
        }
        
        if self.verbose:
            print("开始计算时间基准测试...")
            print(f"数据大小: {len(self.data)} 个数据点")
        
        # 测试滚动回归策略
        for window in rolling_windows:
            if self.verbose:
                print(f"测试滚动回归策略 (窗口={window})...")
            
            strategy = PairsStrategy(
                window=window,
                std_dev_mult=1.2,
                verbose=False
            )
            
            # 模拟引擎初始化
            class MockEngine:
                def __init__(self, data):
                    self.data = data
                    self.commission_rate = 0.0003
                    self.slippage_rate = 0.0001
            
            engine = MockEngine(self.data)
            
            # 测量计算时间
            start_time = time.time()
            strategy.initialize(engine)
            computation_time = time.time() - start_time
            
            results['rolling_regression'][window] = {
                'computation_time': computation_time,
                'time_per_point': computation_time / len(self.data),
                'complexity': 'O(n*w)',  # n个数据点，每个点需要w窗口的计算
                'theoretical_ops': len(self.data) * window
            }
            
            if self.verbose:
                print(f"  窗口{window}: {computation_time:.4f}秒 ({computation_time/len(self.data)*1000:.2f}ms/点)")
        
        # 测试卡尔曼滤波器策略
        if self.verbose:
            print("测试卡尔曼滤波器策略...")
        
        kalman_strategy = KalmanPairsStrategy(
            verbose=False,
            **kalman_params
        )
        
        engine = MockEngine(self.data)
        
        # 测量计算时间
        start_time = time.time()
        kalman_strategy.initialize(engine)
        computation_time = time.time() - start_time
        
        results['kalman_filter'] = {
            'computation_time': computation_time,
            'time_per_point': computation_time / len(self.data),
            'complexity': 'O(n)',  # 线性复杂度
            'theoretical_ops': len(self.data)
        }
        
        if self.verbose:
            print(f"  卡尔曼滤波器: {computation_time:.4f}秒 ({computation_time/len(self.data)*1000:.2f}ms/点)")
        
        # 计算性能提升
        results['performance_improvement'] = {}
        for window in rolling_windows:
            rolling_time = results['rolling_regression'][window]['computation_time']
            kalman_time = results['kalman_filter']['computation_time']
            speedup = rolling_time / kalman_time
            results['performance_improvement'][window] = {
                'speedup_factor': speedup,
                'time_saved': rolling_time - kalman_time,
                'efficiency_gain': (speedup - 1) * 100  # 百分比提升
            }
            
            if self.verbose:
                print(f"  相比窗口{window}的滚动回归，卡尔曼滤波器快 {speedup:.1f}x")
        
        self.results['computation_time'] = results
        return results
    
    def benchmark_memory_usage(self) -> Dict:
        """
        基准测试内存使用
        
        Returns:
            内存使用对比结果
        """
        import psutil
        import os
        
        results = {
            'rolling_regression': {},
            'kalman_filter': {},
            'data_size': len(self.data)
        }
        
        if self.verbose:
            print("开始内存使用基准测试...")
        
        # 获取基准内存使用
        process = psutil.Process(os.getpid())
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试滚动回归策略内存使用
        for window in [10, 20, 40]:
            strategy = PairsStrategy(window=window, verbose=False)
            
            class MockEngine:
                def __init__(self, data):
                    self.data = data
                    self.commission_rate = 0.0003
                    self.slippage_rate = 0.0001
            
            engine = MockEngine(self.data)
            strategy.initialize(engine)
            
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_used = current_memory - baseline_memory
            
            results['rolling_regression'][window] = {
                'memory_mb': memory_used,
                'memory_per_point': memory_used / len(self.data) * 1024  # KB per point
            }
            
            # 清理
            del strategy, engine
        
        # 测试卡尔曼滤波器策略内存使用
        kalman_strategy = KalmanPairsStrategy(verbose=False)
        engine = MockEngine(self.data)
        kalman_strategy.initialize(engine)
        
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = current_memory - baseline_memory
        
        results['kalman_filter'] = {
            'memory_mb': memory_used,
            'memory_per_point': memory_used / len(self.data) * 1024  # KB per point
        }
        
        if self.verbose:
            print(f"卡尔曼滤波器内存使用: {memory_used:.2f} MB")
            for window in [10, 20, 40]:
                rolling_memory = results['rolling_regression'][window]['memory_mb']
                print(f"滚动回归(窗口{window})内存使用: {rolling_memory:.2f} MB")
        
        self.results['memory_usage'] = results
        return results
    
    def benchmark_real_time_processing(self, batch_sizes: List[int] = [1, 10, 100]) -> Dict:
        """
        基准测试实时处理能力
        
        Args:
            batch_sizes: 批处理大小列表
            
        Returns:
            实时处理能力对比结果
        """
        results = {
            'rolling_regression': {},
            'kalman_filter': {},
            'batch_sizes': batch_sizes
        }
        
        if self.verbose:
            print("开始实时处理能力基准测试...")
        
        # 准备策略
        rolling_strategy = PairsStrategy(window=20, verbose=False)
        kalman_strategy = KalmanPairsStrategy(verbose=False)
        
        class MockEngine:
            def __init__(self, data):
                self.data = data
                self.commission_rate = 0.0003
                self.slippage_rate = 0.0001
                self.portfolio = {'positions': {}, 'cash': 1000000}
        
        # 初始化策略
        engine = MockEngine(self.data)
        rolling_strategy.initialize(engine)
        kalman_strategy.initialize(engine)
        
        # 测试不同批处理大小的处理速度
        for batch_size in batch_sizes:
            if self.verbose:
                print(f"测试批处理大小: {batch_size}")
            
            # 准备测试数据
            test_data = self.data.iloc[:min(1000, len(self.data))]  # 使用前1000个数据点
            
            # 测试滚动回归策略
            rolling_times = []
            for i in range(0, len(test_data), batch_size):
                batch = test_data.iloc[i:i+batch_size]
                start_time = time.time()
                
                for timestamp, row in batch.iterrows():
                    rolling_strategy.on_bar(timestamp, row)
                
                batch_time = time.time() - start_time
                rolling_times.append(batch_time)
            
            # 测试卡尔曼滤波器策略
            kalman_times = []
            for i in range(0, len(test_data), batch_size):
                batch = test_data.iloc[i:i+batch_size]
                start_time = time.time()
                
                for timestamp, row in batch.iterrows():
                    kalman_strategy.on_bar(timestamp, row)
                
                batch_time = time.time() - start_time
                kalman_times.append(batch_time)
            
            # 计算统计数据
            results['rolling_regression'][batch_size] = {
                'avg_batch_time': np.mean(rolling_times),
                'max_batch_time': np.max(rolling_times),
                'throughput': batch_size / np.mean(rolling_times)  # 每秒处理的数据点数
            }
            
            results['kalman_filter'][batch_size] = {
                'avg_batch_time': np.mean(kalman_times),
                'max_batch_time': np.max(kalman_times),
                'throughput': batch_size / np.mean(kalman_times)
            }
            
            if self.verbose:
                rolling_throughput = results['rolling_regression'][batch_size]['throughput']
                kalman_throughput = results['kalman_filter'][batch_size]['throughput']
                print(f"  滚动回归吞吐量: {rolling_throughput:.1f} 点/秒")
                print(f"  卡尔曼滤波器吞吐量: {kalman_throughput:.1f} 点/秒")
                print(f"  性能提升: {kalman_throughput/rolling_throughput:.1f}x")
        
        self.results['real_time_processing'] = results
        return results
    
    def generate_performance_report(self, output_file: str = "performance_report.txt") -> None:
        """
        生成性能报告
        
        Args:
            output_file: 输出文件名
        """
        if not self.results:
            print("请先运行基准测试")
            return
        
        report = []
        report.append("=" * 80)
        report.append("卡尔曼滤波器 vs 滚动回归 性能基准测试报告")
        report.append("=" * 80)
        report.append("")
        
        # 计算时间报告
        if 'computation_time' in self.results:
            report.append("1. 计算时间对比")
            report.append("-" * 40)
            
            ct_results = self.results['computation_time']
            kalman_time = ct_results['kalman_filter']['computation_time']
            
            report.append(f"数据大小: {ct_results['data_size']} 个数据点")
            report.append(f"卡尔曼滤波器总时间: {kalman_time:.4f}秒")
            report.append("")
            
            for window in sorted(ct_results['rolling_regression'].keys()):
                rolling_time = ct_results['rolling_regression'][window]['computation_time']
                speedup = ct_results['performance_improvement'][window]['speedup_factor']
                report.append(f"滚动回归(窗口{window}): {rolling_time:.4f}秒 (慢{speedup:.1f}倍)")
            
            report.append("")
        
        # 内存使用报告
        if 'memory_usage' in self.results:
            report.append("2. 内存使用对比")
            report.append("-" * 40)
            
            mem_results = self.results['memory_usage']
            kalman_memory = mem_results['kalman_filter']['memory_mb']
            
            report.append(f"卡尔曼滤波器内存使用: {kalman_memory:.2f} MB")
            
            for window in sorted(mem_results['rolling_regression'].keys()):
                rolling_memory = mem_results['rolling_regression'][window]['memory_mb']
                report.append(f"滚动回归(窗口{window}): {rolling_memory:.2f} MB")
            
            report.append("")
        
        # 实时处理报告
        if 'real_time_processing' in self.results:
            report.append("3. 实时处理能力对比")
            report.append("-" * 40)
            
            rt_results = self.results['real_time_processing']
            
            for batch_size in rt_results['batch_sizes']:
                rolling_throughput = rt_results['rolling_regression'][batch_size]['throughput']
                kalman_throughput = rt_results['kalman_filter'][batch_size]['throughput']
                improvement = kalman_throughput / rolling_throughput
                
                report.append(f"批处理大小 {batch_size}:")
                report.append(f"  滚动回归吞吐量: {rolling_throughput:.1f} 点/秒")
                report.append(f"  卡尔曼滤波器吞吐量: {kalman_throughput:.1f} 点/秒")
                report.append(f"  性能提升: {improvement:.1f}倍")
                report.append("")
        
        # 总结
        report.append("4. 总结")
        report.append("-" * 40)
        report.append("卡尔曼滤波器相比滚动回归的优势:")
        report.append("• 计算复杂度从O(n*w)降低到O(n)")
        report.append("• 内存使用更少，不需要存储滚动窗口数据")
        report.append("• 实时处理能力更强，适合高频交易")
        report.append("• 能够动态调整模型参数")
        report.append("• 提供不确定性估计")
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        if self.verbose:
            print(f"性能报告已保存到: {output_file}")
            print('\n'.join(report))
