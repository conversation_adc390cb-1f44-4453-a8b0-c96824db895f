"""
性能测试脚本
用于测试优化前后的回测性能差异
"""

import time
import numpy as np
import pandas as pd
from datetime import datetime
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.loader import DataLoader
from src.optimization.optimizer import StrategyOptimizer
from src.optimization.cache import clear_cache, get_cache_info
from src.engine.engine import BacktestEngine
from src.strategy.pairs_strategy import PairsStrategy

def create_test_data(n_days=500):
    """创建测试数据"""
    dates = pd.date_range('2023-01-01', periods=n_days, freq='D')
    
    # 生成相关的价格序列
    np.random.seed(42)
    price1 = 100 + np.cumsum(np.random.randn(n_days) * 0.02)
    price2 = 50 + np.cumsum(np.random.randn(n_days) * 0.015)
    
    # 添加一些相关性
    correlation_factor = 0.7
    price2 = price2 + correlation_factor * (price1 - 100) * 0.3
    
    data = pd.DataFrame({
        '159915': price1,
        '518880': price2
    }, index=dates)
    
    return data

def test_single_backtest_performance():
    """测试单次回测性能"""
    print("=== 单次回测性能测试 ===")
    
    # 创建测试数据
    data = create_test_data(1000)  # 1000天数据
    
    # 测试参数
    params = {
        'window': 60,
        'std_dev_mult': 2.0,
        'max_pos_size': 1.0
    }
    
    backtest_params = {
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'slippage_rate': 0.0001
    }
    
    # 运行多次测试取平均值
    n_runs = 5
    times = []
    
    for i in range(n_runs):
        start_time = time.time()
        
        strategy = PairsStrategy(**params, verbose=False)
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=backtest_params['initial_capital'],
            commission_rate=backtest_params['commission_rate'],
            slippage_rate=backtest_params['slippage_rate'],
            verbose=False
        )
        results = engine.run()
        
        end_time = time.time()
        times.append(end_time - start_time)
        
        print(f"第 {i+1} 次运行: {times[-1]:.3f}秒")
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    
    print(f"\n单次回测平均时间: {avg_time:.3f} ± {std_time:.3f}秒")
    print(f"最终收益率: {((results['equity_curve'][-1] / results['equity_curve'][0]) - 1) * 100:.2f}%")
    print(f"交易次数: {len(results['trades'])}")
    
    return avg_time

def test_optimization_performance():
    """测试参数优化性能"""
    print("\n=== 参数优化性能测试 ===")
    
    # 创建测试数据
    data = create_test_data(500)  # 500天数据，减少测试时间
    
    backtest_params = {
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'slippage_rate': 0.0001
    }
    
    # 小规模参数范围用于测试
    param_ranges = {
        'window': [30, 60, 90],
        'std_dev_mult': [1.0, 2.0, 3.0],
        'max_pos_size': [1.0]
    }
    
    total_combinations = np.prod([len(v) for v in param_ranges.values()])
    print(f"参数组合数量: {total_combinations}")
    
    # 清空缓存
    clear_cache()
    
    # 测试单进程模式
    print("\n--- 单进程模式 ---")
    optimizer = StrategyOptimizer(data, backtest_params)
    
    start_time = time.time()
    best_params, best_results, all_results = optimizer.optimize(
        param_ranges=param_ranges,
        n_jobs=1,
        metric='total_returns'
    )
    single_process_time = time.time() - start_time
    
    print(f"单进程优化时间: {single_process_time:.3f}秒")
    print(f"最优参数: {best_params}")
    
    # 测试多进程模式（使用缓存）
    print("\n--- 多进程模式（使用缓存） ---")
    start_time = time.time()
    best_params_mp, best_results_mp, all_results_mp = optimizer.optimize(
        param_ranges=param_ranges,
        n_jobs=4,
        metric='total_returns'
    )
    multi_process_time = time.time() - start_time
    
    print(f"多进程优化时间: {multi_process_time:.3f}秒")
    print(f"最优参数: {best_params_mp}")
    
    # 显示缓存信息
    cache_info = get_cache_info()
    print(f"\n缓存信息: {cache_info}")
    
    # 计算加速比
    if multi_process_time > 0:
        speedup = single_process_time / multi_process_time
        print(f"\n多进程加速比: {speedup:.2f}x")
    
    return single_process_time, multi_process_time

def test_memory_usage():
    """测试内存使用情况"""
    print("\n=== 内存使用测试 ===")
    
    try:
        import psutil
        process = psutil.Process()
        
        # 基准内存使用
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"基准内存使用: {baseline_memory:.1f} MB")
        
        # 创建大数据集
        data = create_test_data(2000)  # 2000天数据
        data_memory = process.memory_info().rss / 1024 / 1024
        print(f"加载数据后内存: {data_memory:.1f} MB (+{data_memory - baseline_memory:.1f} MB)")
        
        # 运行优化
        backtest_params = {
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'slippage_rate': 0.0001
        }
        
        param_ranges = {
            'window': [30, 60],
            'std_dev_mult': [1.0, 2.0],
            'max_pos_size': [1.0]
        }
        
        optimizer = StrategyOptimizer(data, backtest_params)
        best_params, best_results, all_results = optimizer.optimize(
            param_ranges=param_ranges,
            n_jobs=2,
            metric='total_returns'
        )
        
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"优化完成后内存: {final_memory:.1f} MB (+{final_memory - baseline_memory:.1f} MB)")
        
    except ImportError:
        print("需要安装 psutil 来测试内存使用: pip install psutil")

def main():
    """主测试函数"""
    print("开始性能测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 单次回测性能测试
    single_backtest_time = test_single_backtest_performance()
    
    # 参数优化性能测试
    single_opt_time, multi_opt_time = test_optimization_performance()
    
    # 内存使用测试
    test_memory_usage()
    
    # 总结
    print("\n" + "="*50)
    print("性能测试总结:")
    print(f"单次回测平均时间: {single_backtest_time:.3f}秒")
    print(f"单进程优化时间: {single_opt_time:.3f}秒")
    print(f"多进程优化时间: {multi_opt_time:.3f}秒")
    
    if multi_opt_time > 0:
        speedup = single_opt_time / multi_opt_time
        print(f"多进程加速比: {speedup:.2f}x")
    
    print("\n优化建议:")
    if single_backtest_time < 0.1:
        print("✓ 单次回测性能良好")
    else:
        print("⚠ 单次回测可能需要进一步优化")
    
    if speedup > 2:
        print("✓ 多进程优化效果显著")
    elif speedup > 1.5:
        print("✓ 多进程优化有一定效果")
    else:
        print("⚠ 多进程优化效果有限，可能需要调整")

if __name__ == "__main__":
    main()
